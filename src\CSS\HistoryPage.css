/* src/CSS/HistoryPage.css */

.history-page {
  padding: 2rem;
  background-color: #f8fafc;
  min-height: 100vh;
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 1rem;
}

.loading-icon {
  font-size: 2rem;
}

.loading-text {
  color: #6b7280;
}

/* Header */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.history-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  color: #374151;
  text-decoration: none;
}

.action-button:hover {
  background-color: #f9fafb;
}

/* Filters */
.filters-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-container {
  min-width: 200px;
}

.search-input {
  width: 200px;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background-color: white;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background-color: white;
  cursor: pointer;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
}

.date-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background-color: white;
  cursor: pointer;
}

.filter-button {
  padding: 0.5rem 1rem;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  font-weight: 500;
}

.filter-button:hover {
  background-color: #059669;
}

/* Table Container */
.table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-wrapper {
  overflow-x: auto;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
}

.table-header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.table-header th {
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-body tr {
  border-bottom: 1px solid #f3f4f6;
}

.table-body tr:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 1rem;
  font-size: 0.875rem;
  color: #111827;
}

.equipment-name {
  font-weight: 500;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-released {
  background-color: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.status-returned {
  background-color: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.status-approved {
  background-color: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.status-rejected {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.date-cell {
  color: #6b7280;
}

.date-time {
  font-size: 0.75rem;
  color: #9ca3af;
}

.view-button {
  padding: 0.25rem 0.5rem;
  background-color: transparent;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s;
}

.view-button:hover {
  color: #3b82f6;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid #f3f4f6;
  background-color: #f9fafb;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  gap: 0.25rem;
}

.pagination-button {
  padding: 0.5rem 0.75rem;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  color: #374151;
}

.pagination-button:hover:not(:disabled) {
  background-color: #f9fafb;
}

.pagination-button:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
  color: #9ca3af;
}

.pagination-button.active {
  background-color: #10b981;
  color: white;
  font-weight: 600;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.empty-message {
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.25rem;
}

.modal-close:hover {
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
}

.modal-details {
  display: grid;
  gap: 1rem;
}

.detail-item {
  margin-bottom: 1rem;
}

.detail-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.detail-value {
  font-size: 1rem;
  color: #111827;
}

/* Enhanced Modal Styles */

/* Enhanced Modal */
.enhanced-modal {
  max-width: 900px;
  max-height: 80vh;
  width: 90%;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  border-bottom: 2px solid #e0e7ff;
  margin-bottom: 20px;
  background-color: #f8fafc;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.tab-button {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background-color: #e2e8f0;
  color: #475569;
}

.tab-button.active {
  background-color: #3b82f6;
  color: white;
  border-bottom-color: #1d4ed8;
}

/* Tab Content */
.tab-content {
  min-height: 300px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Usage Report Styles */
.usage-report {
  padding: 20px 0;
}

.report-title {
  margin-bottom: 20px;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.usage-table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.usage-table {
  width: 100%;
  border-collapse: collapse;
}

.usage-table th {
  background-color: #f1f5f9;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e2e8f0;
}

.usage-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  color: #4b5563;
}

.usage-table tbody tr:hover {
  background-color: #f8fafc;
}

/* Usage Summary */
.usage-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.summary-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.summary-title {
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

/* Modal Footer */
.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  border-radius: 0 0 12px 12px;
}

.close-button {
  padding: 8px 24px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background: #4b5563;
}

/* Responsive Design */
@media (max-width: 768px) {
  .history-page {
    padding: 1rem;
  }

  .history-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .header-actions {
    justify-content: flex-start;
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }

  .filter-select,
  .date-input {
    width: 100%;
  }

  .table-wrapper {
    overflow-x: scroll;
  }

  .history-table {
    min-width: 800px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .pagination-controls {
    justify-content: center;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header,
  .modal-body {
    padding: 1rem;
  }

  /* Enhanced modal responsive styles */
  .enhanced-modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .tab-navigation {
    flex-direction: column;
  }
  
  .tab-button {
    text-align: center;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .usage-summary {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .table-header th,
  .table-cell {
    padding: 0.5rem;
  }

  .action-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
}