/* Dashboard Main Layout */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.dashboard-main {
  margin-left: 250px;
  flex: 1;
  min-height: 100vh;
  background-color: transparent;
  transition: margin-left 0.3s ease;
}

.dashboard-inner {
  width: 1800px;
  margin: 0;
  padding: 40px 20px;
  min-height: calc(100vh - 80px);
}

/* Specific styling for non-equipment pages to maintain centering */
.dashboard-inner:not(:has(.equipment-page)) {
  max-width: 1500px;
  margin: 0 auto;
}

/* Alternative approach using CSS container queries (fallback) */
.dashboard-content-centered {
  max-width: 1200px;
  margin: 0 auto;
}

/* Dashboard Overview */
.dashboard-welcome {
  margin-bottom: 40px;
  padding: 30px;
  background:#2aa59d;
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(42, 165, 157, 0.3);
  position: relative;
  overflow: hidden;
}

.dashboard-welcome::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

.dashboard-welcome h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.dashboard-welcome p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  position: relative;
  z-index: 2;
}

/* Stats Cards Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 30px;
}

.stat-card {
  padding: 28px;
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card.blue::before {
  background: linear-gradient(135deg, #2aa59d, #43b091);
}

.stat-card.blue h3 {
  color: #2aa59d;
}

.stat-card.green::before {
  background: linear-gradient(135deg, #10b981, #059669);
}

.stat-card.green h3 {
  color: #059669;
}

.stat-card.red::before {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-card.red h3 {
  color: #dc2626;
}

.stat-card p {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  color: #1f2937;
}

/* Section Headers */
.section-header {
  margin-bottom: 30px;
}

.section-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
}

.section-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

/* Button Styles */
.btn-group {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: #2aa59d;
  color: #fff;
  box-shadow: 0 4px 12px rgba(42, 165, 157, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(42, 165, 157, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #fff;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
}

.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #fff;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: #fff;
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(107, 114, 128, 0.4);
}

/* Content Cards */
.content-card {
  margin-top: 30px;
  padding: 28px;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.content-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
}

/* User List */
.user-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-item {
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-item:last-child {
  border-bottom: none;
}

.user-info {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.user-status {
  font-size: 12px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  color: #065f46;
}

.status-pending {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #92400e;
}

/* Profile Section */
.profile-grid {
  margin-top: 30px;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
}

.profile-card {
  padding: 28px;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.profile-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.profile-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
}

/* Profile Picture */
.profile-picture {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #2aa59d, #43b091);
  border-radius: 50%;
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(42, 165, 157, 0.3);
}

.profile-picture:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 32px rgba(42, 165, 157, 0.4);
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 14px;
  color: #374151;
  background-color: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #2aa59d;
  box-shadow: 0 0 0 3px rgba(42, 165, 157, 0.1);
}

/* Announcements Section */
.announcements-section {
  margin-top: 50px;
}

.section-header-with-button {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  gap: 20px;
}

.section-header-with-button .section-header {
  margin-bottom: 0;
  flex: 1;
}

.btn-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* Announcements Grid */
.announcements-grid {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

/* Announcement Cards */
.announcement-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
}

.announcement-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.announcement-card.priority-high {
  border-left: 6px solid #ef4444;
}

.announcement-card.priority-high::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.announcement-card.priority-medium {
  border-left: 6px solid #f59e0b;
}

.announcement-card.priority-medium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.announcement-card.priority-low {
  border-left: 6px solid #10b981;
}

.announcement-card.priority-low::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #10b981, #059669);
}

/* Announcement Header */
.announcement-header {
  padding: 24px 24px 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 15px;
  margin-top: 4px;
}

.announcement-title-section {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.announcement-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  line-height: 1.4;
}

.priority-badge {
  font-size: 10px;
  font-weight: 700;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.priority-badge.high {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  color: #dc2626;
}

.priority-badge.medium {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
  color: #f59e0b;
}

.priority-badge.low {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  color: #10b981;
}

.announcement-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.action-btn:hover {
  transform: scale(1.1);
}

.edit-btn:hover {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

.delete-btn:hover {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
}

/* Announcement Content */
.announcement-content {
  padding: 20px 24px;
}

.announcement-content p {
  margin: 0;
  color: #374151;
  line-height: 1.6;
  font-size: 14px;
}

/* Announcement Footer */
.announcement-footer {
  padding: 0 24px 24px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.announcement-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.announcement-author,
.announcement-date {
  font-size: 12px;
  color: #6b7280;
}

.announcement-author {
  font-weight: 500;
}

.announcement-category {
  font-size: 12px;
  font-weight: 500;
  padding: 6px 14px;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  color: #374151;
  border-radius: 20px;
  white-space: nowrap;
}

/* Empty Announcements State */
.empty-announcements {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
  border-radius: 16px;
  border: 2px dashed #d1d5db;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-announcements h3 {
  color: #374151;
  margin: 0 0 8px 0;
  font-size: 18px;
}

.empty-announcements p {
  color: #6b7280;
  margin: 0;
}

/* Loading states */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6b7280;
}

/* Empty states */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-state h3 {
  color: #374151;
  margin-bottom: 8px;
}

/* Enhanced Dashboard CSS with Analytics */

/* Main Statistics Grid */
.main-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin: 30px 0;
}

.stat-card-large {
  background: white;
  padding: 32px 28px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f5f9;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card-large:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.stat-card-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 20px 20px 0 0;
}

.stat-card-large.primary::before {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-card-large.success::before {
  background: linear-gradient(135deg, #10b981, #059669);
}

.stat-card-large.info::before {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.stat-card-large .stat-number {
  font-size: 3.5rem;
  font-weight: 800;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1;
}

.stat-card-large .stat-label {
  font-size: 1rem;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 16px;
}

.stat-card-large .stat-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  font-size: 2.5rem;
  opacity: 0.1;
}

/* Secondary Statistics Grid */
.secondary-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.stat-card-small {
  background: white;
  padding: 24px 20px;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card-small:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.stat-card-small::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #e5e7eb;
}

.stat-card-small.warning::before {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-card-small.danger::before {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-card-small .stat-number {
  font-size: 2.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.stat-card-small .stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Charts Section */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin: 40px 0;
}

.chart-card {
  background: white;
  padding: 32px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f5f9;
}

.chart-header {
  margin-bottom: 24px;
}

.chart-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.chart-header p {
  color: #6b7280;
  margin: 0;
  font-size: 0.9rem;
}

/* Bar Chart */
.bar-chart {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.bar-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bar-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.bar-container {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.bar-fill {
  height: 24px;
  border-radius: 12px;
  background: #30a79a;
  min-width: 20px;
  transition: width 0.8s ease;
  position: relative;
}

.bar-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  min-width: 30px;
}

/* Activity Card */
.activity-card {
  background: white;
  padding: 32px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f5f9;
}

.activity-header {
  margin-bottom: 24px;
}

.activity-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.activity-header p {
  color: #6b7280;
  margin: 0;
  font-size: 0.9rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: #f1f5f9;
  transform: translateX(4px);
}

.activity-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.activity-icon.success {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
}

.activity-icon.info {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

.activity-icon.warning {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
}

.activity-icon.primary {
  background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.activity-time {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Quick Actions */
.quick-actions {
  margin: 40px 0;
  background: white;
  padding: 32px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f5f9;
}

.quick-actions h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 24px 0;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-btn {
  padding: 16px 20px;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn:hover {
  transform: translateY(-2px);
}

.action-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  width: 100%;
}

.action-btn.primary:hover {
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.action-btn.success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
  width: 100%;
}

.action-btn.success:hover {
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
}

.action-btn.info {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  box-shadow: 0 4px 16px rgba(6, 182, 212, 0.3);
  width: 100%;
}

.action-btn.info:hover {
  box-shadow: 0 8px 24px rgba(6, 182, 212, 0.4);
}

.action-btn.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
  width: 100%;
}

.action-btn.warning:hover {
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.4);
}

/* Enhanced Welcome Section */
.dashboard-welcome {
  margin-bottom: 40px;
  padding: 40px;
  background: linear-gradient(135deg, #2aa59d 0%, #1e7b73 100%);
  border-radius: 20px;
  color: white;
  box-shadow: 0 12px 40px rgba(42, 165, 157, 0.3);
  position: relative;
  overflow: hidden;
}

.dashboard-welcome::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  transform: rotate(45deg);
}

.dashboard-welcome::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -10%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
}

.dashboard-welcome h1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: white;
  margin: 0 0 12px 0;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.dashboard-welcome p {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  position: relative;
  z-index: 2;
}

/* Responsive Design for Analytics */
@media (max-width: 1200px) {
  .charts-section {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .main-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .main-stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .secondary-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .stat-card-large {
    padding: 24px 20px;
  }
  
  .stat-card-large .stat-number {
    font-size: 2.5rem;
  }
  
  .stat-card-small {
    padding: 20px 16px;
  }
  
  .stat-card-small .stat-number {
    font-size: 1.875rem;
  }
  
  .chart-card,
  .activity-card,
  .quick-actions {
    padding: 24px 20px;
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
  }
  
  .dashboard-welcome {
    padding: 32px 24px;
  }
  
  .dashboard-welcome h1 {
    font-size: 2rem;
  }
  
  .bar-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .activity-item {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .secondary-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card-large .stat-number {
    font-size: 2rem;
  }
  
  .stat-card-small .stat-number {
    font-size: 1.5rem;
  }
  
  .dashboard-welcome h1 {
    font-size: 1.75rem;
  }
  
  .dashboard-welcome p {
    font-size: 1rem;
  }
}

/* Loading Animation for Stats */
.stat-number {
  animation: countUp 1.5s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse Animation for Important Stats */
.stat-card-small.danger .stat-number {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Enhanced Card Hover Effects */
.stat-card-large:hover .stat-icon {
  opacity: 0.2;
  transform: scale(1.1);
  transition: all 0.3s ease;
}

.activity-item:hover .activity-icon {
  transform: scale(1.1);
  transition: all 0.3s ease;
}

/* Gradient Text Effect */
.chart-header h3 {
  background: linear-gradient(135deg, #1f2937, #374151);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced Scrollbar for Chart Areas */
.chart-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.chart-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.chart-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
}

.chart-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-main {
    margin-left: 200px;
  }
  
  .profile-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    margin-left: 0;
    width: 100%;
  }
  
  .dashboard-inner {
    padding: 20px 16px;
  }
  
  .dashboard-inner:not(:has(.equipment-page)) {
    max-width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .btn-group {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .user-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .profile-picture {
    width: 100px;
    height: 100px;
    font-size: 40px;
  }
  
  .section-header h1 {
    font-size: 24px;
  }
  
  .dashboard-welcome h1 {
    font-size: 28px;
  }
  
  .section-header-with-button {
    flex-direction: column;
    align-items: stretch;
  }
  
  .announcements-grid {
    grid-template-columns: 1fr;
  }
  
  .announcement-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .announcement-title-section {
    justify-content: space-between;
  }
  
  .announcement-actions {
    align-self: flex-end;
  }
  
  .announcement-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .dashboard-inner {
    padding: 16px 12px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .content-card {
    padding: 20px;
  }
  
  .profile-card {
    padding: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header h1 {
    font-size: 22px;
  }
  
  .dashboard-welcome h1 {
    font-size: 24px;
  }
  
  .announcements-grid {
    grid-template-columns: 1fr;
  }
  
  .announcement-card {
    margin: 0 -12px;
    border-radius: 12px;
  }
  
  .announcement-header,
  .announcement-content,
  .announcement-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
}

/* Animation for smooth transitions */
.dashboard-inner > * {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-between {
  justify-content: space-between;
}


.items-center {
  align-items: center;
}