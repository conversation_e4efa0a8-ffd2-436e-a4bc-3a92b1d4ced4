/* src/components/Equipment.css */

/* Main container */
.equipment-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #ffffff;
  min-height: 100vh;
}

/* Page header */
.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #374151;
  margin: 0 0 10px 0;
}

.page-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
}

/* Navigation tabs */
.nav-tabs {
  display: flex;
  margin-bottom: 30px;
  border-bottom: 2px solid #14b8a6;
  background-color: white;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background-color: #f0fdfa;
  color: #374151;
}

.nav-tab.active {
  background-color: #14b8a6;
  color: white;
  border-bottom-color: #0d9488;
}

.nav-tab-icon {
  font-size: 16px;
}

/* Section headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.section-header-left {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.category-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Buttons - Fixed styling */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  line-height: 1.2;
  box-sizing: border-box;
  min-height: 36px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 14px;
  line-height: 1;
}

.btn-primary {
  background-color: #14b8a6;
  color: white;
  border: 1px solid #14b8a6;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0d9488;
  border-color: #0d9488;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
  border: 1px solid #6b7280;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
  border-color: #4b5563;
}

.btn-success {
  background-color: #10b981;
  color: white;
  border: 1px solid #10b981;
}

.btn-success:hover:not(:disabled) {
  background-color: #059669;
  border-color: #059669;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
  border: 1px solid #ef4444;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
  border-color: #dc2626;
}

.btn-outline {
  background-color: #ffffff;
  color: #14b8a6;
  border: 1px solid #14b8a6;
}

.btn-outline:hover:not(:disabled) {
  background-color: #f0fdfa;
  border-color: #0d9488;
  color: #0d9488;
}

.btn-disabled {
  background-color: #d1d5db;
  color: #9ca3af;
  border: 1px solid #d1d5db;
  cursor: not-allowed;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
  min-height: 24px;
}

.btn-xs {
  padding: 2px 4px;
  font-size: 9px;
  min-height: 18px;
  line-height: 1.1;
}

/* Categories grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.category-card {
  background: white;
  border-radius: 12px;
  border: 2px solid #14b8a6;
  padding: 20px;
  transition: all 0.2s ease;
  cursor: default;
  box-shadow: 0 1px 3px rgba(20, 184, 166, 0.1);
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(20, 184, 166, 0.15);
  border-color: #0d9488;
}

.category-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.category-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 4px 0;
}

.category-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.category-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 1px solid #14b8a6;
  border-bottom: 1px solid #14b8a6;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
  line-height: 1;
}

.stat-number.available {
  color: #14b8a6;
}

.stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
  margin-top: 4px;
}

.category-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Search bar */
.search-bar {
  position: relative;
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #14b8a6;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  color: #374151;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.search-input::placeholder {
  color: #6b7280;
}

.search-input:focus {
  outline: none;
  border-color: #0d9488;
  box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #14b8a6;
  font-size: 16px;
}

/* Equipment table */
.equipment-table-container {
  background: white;
  border-radius: 12px;
  border: 1px solid #14b8a6;
  box-shadow: 0 1px 3px rgba(20, 184, 166, 0.1);
  overflow: hidden;
}

.table-header {
  padding: 20px;
  border-bottom: 1px solid #14b8a6;
  background-color: #f0fdfa;
}

.table-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.table-wrapper {
  overflow-x: auto;
}

.equipment-table {
  width: 100%;
  border-collapse: collapse;
}

.equipment-table th {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #14b8a6;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  background-color: #f0fdfa;
  white-space: nowrap;
}

.equipment-table td {
  padding: 16px;
  border-bottom: 1px solid #d6f5f2;
  vertical-align: top;
}

.equipment-row {
  transition: background-color 0.2s ease;
}

.equipment-row:hover {
  background-color: #f0fdfa;
}

.equipment-cell {
  min-width: 200px;
}

.equipment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.equipment-name {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.equipment-model {
  font-size: 13px;
  color: #6b7280;
}

.warranty-status {
  font-size: 12px;
  font-weight: 500;
  margin-top: 2px;
}

.warranty-status.expired {
  color: #ef4444;
}

.warranty-status.warning {
  color: #f59e0b;
}

.warranty-status.valid {
  color: #10b981;
}

.serial-cell {
  min-width: 140px;
}

.serial-number {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  background-color: #f0fdfa;
  padding: 4px 8px;
  border-radius: 4px;
  color: #374151;
  border: 1px solid #14b8a6;
}

.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
  white-space: nowrap;
}

.condition-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.condition-badge.excellent {
  background-color: #d1fae5;
  color: #065f46;
}

.condition-badge.good {
  background-color: #dcfce7;
  color: #166534;
}

.condition-badge.fair {
  background-color: #fef3c7;
  color: #92400e;
}

.condition-badge.poor {
  background-color: #fee2e2;
  color: #991b1b;
}

.location-cell,
.assigned-cell {
  min-width: 120px;
  font-size: 14px;
  color: #374151;
}

.actions-cell {
  min-width: 140px;
}

.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
}

/* Empty states */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.8;
  color: #14b8a6;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-message {
  font-size: 1rem;
  margin: 0 0 16px 0;
  line-height: 1.5;
  color: #6b7280;
}

/* Loading states */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: #ffffff;
}

.loading-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(20, 184, 166, 0.1);
  border: 1px solid #14b8a6;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #d6f5f2;
  border-top: 4px solid #14b8a6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(20, 184, 166, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(20, 184, 166, 0.25);
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  animation: modalSlideIn 0.3s ease-out;
  border: 1px solid #14b8a6;
}

.category-modal {
  max-width: 500px;
}

.equipment-modal {
  max-width: 700px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 30px;
  border-bottom: 1px solid #14b8a6;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modal-close:hover {
  background-color: #f0fdfa;
  color: #374151;
}

.modal-form {
  padding: 30px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #d6f5f2;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Form styles */
.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-label.required::after {
  content: " *";
  color: #ef4444;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #14b8a6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
  background-color: white;
  color: #374151;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #6b7280;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #0d9488;
  box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.color-input-wrapper {
  position: relative;
}

.color-input {
  padding-left: 40px;
}

.color-preview {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #14b8a6;
}

/* Tab content */
.tab-content {
  background: white;
  border-radius: 0 0 8px 8px;
  padding: 20px;
  border: 1px solid #14b8a6;
  border-top: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .equipment-page {
    padding: 16px;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .section-header-right {
    flex-direction: column;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .category-actions {
    flex-direction: column;
    gap: 4px;
  }
  
  .nav-tabs {
    flex-direction: column;
  }
  
  .nav-tab {
    justify-content: center;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .modal {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }
  
  .modal-form {
    padding: 20px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .table-wrapper {
    font-size: 13px;
  }
  
  .equipment-table th,
  .equipment-table td {
    padding: 12px 8px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .equipment-table th,
  .equipment-table td {
    padding: 8px 6px;
  }
  
  .btn {
    font-size: 13px;
    padding: 8px 12px;
  }
  
  .btn-sm {
    font-size: 11px;
    padding: 6px 10px;
  }
  
  .btn-xs {
    font-size: 10px;
    padding: 4px 6px;
  }
}