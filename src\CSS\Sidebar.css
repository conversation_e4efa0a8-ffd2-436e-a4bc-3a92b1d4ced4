/* src/CSS/Sidebar.css */

.sidebar {
  width: 250px;
  height: 100vh;
  background: #248b85 ;
  color: #fff;
  padding: 20px 0;
  position: fixed;
  left: 0;
  top: 0;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 15px rgba(42, 165, 157, 0.3);
  z-index: 1000;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 0 20px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 20px;
}

.sidebar-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  transition: opacity 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.collapsed .sidebar-title {
  opacity: 0;
  display: none;
}

.toggle-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #fff;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.toggle-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.sidebar-nav {
  flex: 1;
  padding: 0 10px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  margin: 4px 0;
  cursor: pointer;
  background-color: transparent;
  border: none;
  border-radius: 10px;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  position: relative;
  overflow: hidden;
  width: calc(100% - 8px);
  box-sizing: border-box;
}

.collapsed .menu-item {
  padding: 14px 8px;
  margin: 4px 5px;
  justify-content: center;
  width: calc(100% - 10px);
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.menu-item:hover::before {
  left: 100%;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-item.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  border-left: 5px solid #ffffff;
  transform: translateX(3px);
}

.collapsed .menu-item.active {
  border-left: none;
  border: 2px solid rgba(255, 255, 255, 0.6);
  transform: none;
}

.menu-item-icon {
  margin-right: 12px;
  font-size: 20px;
  min-width: 20px;
  text-align: center;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  flex-shrink: 0;
}

.collapsed .menu-item-icon {
  margin-right: 0;
  font-size: 20px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-item-label {
  font-size: 14px;
  font-weight: 500;
  transition: opacity 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.collapsed .menu-item-label {
  opacity: 0;
  display: none;
}

.logout-button {
  margin: 20px 15px;
  padding: 12px 18px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.logout-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.logout-button:hover::before {
  left: 100%;
}

.logout-button:hover {
  background: rgba(229, 62, 62, 0.2);
  border-color: rgba(229, 62, 62, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(229, 62, 62, 0.2);
}

.collapsed .logout-button {
  margin: 20px 12px;
  padding: 12px;
  min-width: 46px;
  width: calc(100% - 24px);
  box-sizing: border-box;
}

.logout-button-text {
  transition: opacity 0.3s ease;
}

.collapsed .logout-button-text {
  opacity: 0;
  display: none;
}

.logout-button-icon {
  display: none;
  font-size: 16px;
}

.collapsed .logout-button-icon {
  display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    box-shadow: none;
    background: linear-gradient(90deg, #2aa59d 0%, #43b091 100%);
  }
  
  .sidebar.collapsed {
    width: 100%;
  }
  
  .sidebar-header {
    padding: 0 20px;
  }
  
  .menu-item {
    padding: 16px 20px;
    margin: 6px 10px;
  }
  
  .logout-button {
    margin: 20px;
  }
}

/* Enhanced tooltip for collapsed state */
.menu-item.tooltip {
  position: relative;
}

.collapsed .menu-item:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 80px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #2aa59d 0%, #43b091 100%);
  color: #fff;
  padding: 10px 14px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 1001;
  box-shadow: 0 4px 16px rgba(42, 165, 157, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.collapsed .menu-item:hover::before {
  content: '';
  position: absolute;
  left: 70px;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-right-color: #2aa59d;
  z-index: 1001;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}