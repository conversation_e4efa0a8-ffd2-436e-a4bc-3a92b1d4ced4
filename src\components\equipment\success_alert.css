/* src/components/equipment/success_alert.css */

.success-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
}

.success-modal-container {
  background-color: #fff;
  padding: 30px;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  text-align: center;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.success-modal-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #10b981;
}

.success-modal-title {
  margin: 0 0 15px 0;
  font-size: 20px;
  color: #1f2937;
}

.success-modal-message {
  margin: 0 0 25px 0;
  color: #6b7280;
}

.success-modal-button {
  padding: 12px 24px;
  background-color: #3b82f6;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.success-modal-button:hover {
  background-color: #2563eb;
}