/* Categories Tab Styles */

.categories-container {
  padding: 0 40px;
}

/* Header Section */
.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 30px 0 25px 0;
  border-bottom: 2px solid #f1f5f9;
}

.categories-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #2aa59d;
  text-shadow: 0 1px 2px rgba(42, 165, 157, 0.1);
}

/* Add Category Button */
.add-category-btn {
  padding: 16px 32px;
  background: #2aa59d;
  color: #fff;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 700;
  box-shadow: 0 8px 32px rgba(42, 165, 157, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  min-width: 200px;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-category-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: left 0.5s ease;
}

.add-category-btn:hover::before {
  left: 100%;
}

.add-category-btn:hover {
  background: #43b091;
  box-shadow: 0 12px 36px rgba(67, 176, 145, 0.4);
  transform: translateY(-2px);
}

.add-category-btn-icon {
  font-size: 20px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-category-btn:hover .add-category-btn-icon {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

/* Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

/* Category Card */
.category-card {
  padding: 24px;
  background: white;
  border-radius: 16px;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #2aa59d;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.category-card:hover::before {
  transform: scaleX(1);
}

.category-card:hover {
  border-color: #2aa59d;
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(42, 165, 157, 0.2);
}

/* Category Card Header */
.category-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.category-icon {
  width: 48px;
  height: 48px;
  background: rgba(42, 165, 157, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  transition: all 0.3s ease;
  color: #2aa59d;
}

.category-card:hover .category-icon {
  transform: scale(1.1) rotate(5deg);
  background: rgba(42, 165, 157, 0.15);
}

.category-card-title {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
}

/* Category Stats */
.category-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: 800;
  color: #2aa59d;
  margin-bottom: 4px;
}

.stat-value.available {
  color: #10b981;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Category Actions */
.category-actions {
  display: flex;
  gap: 8px;
}

.category-btn {
  padding: 10px 16px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-btn-primary {
  flex: 1;
  background: #f8fafc;
  color: #475569;
  border-color: #cbd5e1;
}

.category-btn-primary:hover {
  background: #2aa59d;
  color: white;
  border-color: #2aa59d;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(42, 165, 157, 0.3);
}

.category-btn-secondary {
  background: #f1f5f9;
  color: #64748b;
  border-color: #cbd5e1;
}

.category-btn-secondary:hover {
  background: #43b091;
  color: white;
  border-color: #43b091;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(67, 176, 145, 0.3);
}

.category-btn-danger {
  background: #fee2e2;
  color: #dc2626;
  border-color: #f87171;
}

.category-btn-danger:hover {
  background: #dc2626;
  color: white;
  border-color: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Empty State */
.categories-empty-state {
  padding: 60px 40px;
  text-align: center;
  color: #64748b;
  background: white;
  border-radius: 20px;
  border: 2px dashed #cbd5e1;
  margin: 40px 0;
}

.empty-state-icon {
  font-size: 72px;
  margin-bottom: 24px;
  color: #2aa59d;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0,-15px,0); }
  70% { transform: translate3d(0,-7px,0); }
  90% { transform: translate3d(0,-2px,0); }
}

.empty-state-title {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 24px;
  font-weight: 700;
}

.empty-state-message {
  margin: 0;
  font-size: 16px;
  color: #6b7280;
  line-height: 1.5;
}

/* Success Modal Styles */
.success-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.success-modal-container {
  background: white;
  padding: 40px;
  border-radius: 20px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  text-align: center;
  animation: modalSlideIn 0.3s ease-out;
  border-top: 4px solid #2aa59d;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.success-modal-icon {
  font-size: 48px;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
  color: #10b981;
}

.success-modal-title {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 700;
  color: #059669;
}

.success-modal-message {
  margin: 0 0 24px 0;
  font-size: 16px;
  color: #374151;
  line-height: 1.5;
}

.success-modal-button {
  padding: 12px 32px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.success-modal-button:hover {
  background: #059669;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

/* Delete Confirmation Modal Styles */
.delete-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.delete-modal-container {
  background: white;
  padding: 40px;
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  text-align: center;
  animation: modalSlideIn 0.3s ease-out;
  border-top: 4px solid #dc2626;
}

.delete-modal-icon {
  font-size: 56px;
  margin-bottom: 20px;
  animation: shake 0.5s ease-in-out;
  color: #dc2626;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.delete-modal-title {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 700;
  color: #dc2626;
}

.delete-modal-message {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #374151;
  line-height: 1.5;
}

.delete-modal-warning {
  margin: 0 0 32px 0;
  font-size: 14px;
  color: #dc2626;
  background: #fee2e2;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #dc2626;
  text-align: left;
  line-height: 1.5;
}

.delete-modal-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.delete-modal-cancel {
  padding: 12px 24px;
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.delete-modal-cancel:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-2px);
}

.delete-modal-confirm {
  padding: 12px 24px;
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.delete-modal-confirm:hover {
  background: #b91c1c;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

/* Category Search and Filter */
.categories-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 30px;
  align-items: center;
  flex-wrap: wrap;
}

.category-search {
  flex: 1;
  min-width: 300px;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  background: white;
  transition: all 0.3s ease;
}

.category-search:focus {
  outline: none;
  border-color: #2aa59d;
  box-shadow: 0 0 0 4px rgba(42, 165, 157, 0.1);
}

.category-sort-select {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  min-width: 150px;
  background: white;
  transition: all 0.3s ease;
}

.category-sort-select:focus {
  outline: none;
  border-color: #2aa59d;
  box-shadow: 0 0 0 4px rgba(42, 165, 157, 0.1);
}

/* Category Statistics Overview */
.categories-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(42, 165, 157, 0.08);
  border: 1px solid rgba(42, 165, 157, 0.1);
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #2aa59d;
  border-radius: 16px 16px 0 0;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(42, 165, 157, 0.12);
}

.overview-number {
  font-size: 32px;
  font-weight: 700;
  color: #2aa59d;
  margin-bottom: 8px;
}

.overview-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Category Card Enhanced Details */
.category-description {
  color: #64748b;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 3px solid #2aa59d;
}

.category-last-updated {
  font-size: 12px;
  color: #9ca3af;
  text-align: right;
  margin-top: 12px;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .categories-container {
    padding: 0 20px;
  }
  
  .categories-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .category-card {
    padding: 20px;
  }
  
  .category-actions {
    flex-direction: column;
  }
  
  .category-btn {
    width: 100%;
    justify-content: center;
  }
  
  .categories-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .category-search {
    min-width: auto;
  }
  
  .categories-overview {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .delete-modal-actions {
    flex-direction: column;
  }
  
  .delete-modal-cancel,
  .delete-modal-confirm {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .categories-container {
    padding: 0 16px;
  }
  
  .category-card {
    padding: 16px;
  }
  
  .category-stats {
    padding: 12px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .categories-overview {
    grid-template-columns: 1fr;
  }
  
  .empty-state-icon {
    font-size: 56px;
  }
  
  .success-modal-container,
  .delete-modal-container {
    padding: 30px 20px;
    margin: 20px;
  }
}

/* Loading States */
.category-card.loading {
  pointer-events: none;
  opacity: 0.6;
}

.category-card.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #2aa59d;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Focus states for accessibility */
.category-card:focus {
  outline: 3px solid rgba(42, 165, 157, 0.4);
  outline-offset: 2px;
}

.category-card:focus:not(:focus-visible) {
  outline: none;
}

/* Animation for category cards */
.category-card {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}