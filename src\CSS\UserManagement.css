/* src/CSS/UserManagement.css */
.user-management {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  position: relative;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(248, 249, 250, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  backdrop-filter: blur(4px);
}

.loading-spinner {
  background: white;
  padding: 2rem 3rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(42, 165, 157, 0.15);
  font-size: 1.1rem;
  color: #495057;
  border-top: 4px solid #2aa59d;
  text-align: center;
}

.loading-spinner::before {
  content: '⏳';
  font-size: 24px;
  display: block;
  margin-bottom: 10px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Header Section */
.user-management-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 30px;
  background: linear-gradient(135deg, #2aa59d 0%, #43b091 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(42, 165, 157, 0.3);
  position: relative;
  overflow: hidden;
}

.user-management-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

.header-left h1 {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.header-left p {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-size: 1rem;
  position: relative;
  z-index: 2;
}

/* Controls Section */
.user-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 8px rgba(42, 165, 157, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: #2aa59d;
  box-shadow: 0 0 0 4px rgba(42, 165, 157, 0.1);
  transform: translateY(-1px);
}

.filter-group {
  display: flex;
  gap: 1rem;
}

.filter-select {
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background-color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(42, 165, 157, 0.05);
}

.filter-select:focus {
  outline: none;
  border-color: #2aa59d;
  box-shadow: 0 0 0 4px rgba(42, 165, 157, 0.1);
}

/* Table Styles */
.table-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(42, 165, 157, 0.1);
  overflow: hidden;
  border: 1px solid rgba(42, 165, 157, 0.1);
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: #f8fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2aa59d;
  border-bottom: 2px solid #e2e8f0;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.85rem;
}

.users-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  position: relative;
}

.users-table th.sortable:hover {
  background: rgba(42, 165, 157, 0.1);
  color: #43b091;
}

.users-table td {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: middle;
  transition: all 0.3s ease;
}

.users-table tr:hover {
  background: rgba(42, 165, 157, 0.05);
}

.user-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2aa59d 0%, #43b091 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  box-shadow: 0 2px 8px rgba(42, 165, 157, 0.3);
}

/* Badge Styles */
.role-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-admin {
  background-color: #2aa59d;
  color: white;
}

.role-manager {
  background-color: #43b091;
  color: white;
}

.role-user {
  background-color: #6b7280;
  color: white;
}

.role-teacher {
  background-color: #2aa59d;
  color: white;
}

.role-student {
  background-color: #43b091;
  color: white;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-success {
  background-color: #10b981;
  color: white;
}

.status-warning {
  background-color: #f59e0b;
  color: white;
}

.status-danger {
  background-color: #ef4444;
  color: white;
}

.status-secondary {
  background-color: #6b7280;
  color: white;
}

/* Profile Setup Badges */
.profile-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.setup-complete {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.setup-pending {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background-color: #2aa59d;
  color: white;
  box-shadow: 0 2px 8px rgba(42, 165, 157, 0.3);
}

.btn-primary:hover {
  background-color: #43b091;
  box-shadow: 0 4px 16px rgba(42, 165, 157, 0.4);
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

.btn-secondary:hover {
  background-color: #4b5563;
  box-shadow: 0 4px 16px rgba(107, 114, 128, 0.4);
}

.btn-danger {
  background-color: #ef4444;
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
  background-color: #dc2626;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
}

.btn-sm {
  padding: 0.375rem 0.5rem;
  font-size: 0.8rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
  border-top: 4px solid #2aa59d;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 2px solid #f1f5f9;
  position: relative;
}

.modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 1.5rem;
  right: 1.5rem;
  height: 2px;
  background: #2aa59d;
}

.modal-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 700;
}

.modal-close {
  background: #f3f4f6;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background-color: #ef4444;
  color: white;
  transform: scale(1.1);
}

/* Form Styles */
.user-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #4b5563;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: white;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #2aa59d;
  box-shadow: 0 0 0 4px rgba(42, 165, 157, 0.1);
  transform: translateY(-1px);
}

.form-input:hover,
.form-select:hover {
  border-color: #374151;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

/* Statistics Overview */
.user-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.user-stat-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(42, 165, 157, 0.08);
  border: 1px solid rgba(42, 165, 157, 0.1);
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
}

.user-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #2aa59d;
  border-radius: 16px 16px 0 0;
}

.user-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(42, 165, 157, 0.12);
}

.user-stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2aa59d;
  margin-bottom: 8px;
}

.user-stat-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6b7280;
  background: rgba(42, 165, 157, 0.05);
  border-radius: 12px;
  margin: 20px 0;
}

.empty-state::before {
  content: '👥';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* Enhanced Table Interactions */
.users-table tbody tr {
  transition: all 0.3s ease;
}

.users-table tbody tr:hover {
  background: rgba(42, 165, 157, 0.05);
  transform: scale(1.01);
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-management {
    padding: 1rem;
  }
  
  .user-management-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 20px;
  }
  
  .user-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    max-width: none;
  }
  
  .filter-group {
    justify-content: stretch;
  }
  
  .filter-select {
    flex: 1;
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  .users-table {
    min-width: 800px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .user-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .user-management {
    padding: 0.5rem;
  }
  
  .user-management-header {
    padding: 15px;
  }
  
  .user-stats {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: 10px;
    border-radius: 12px;
  }
}

/* Loading states for buttons */
.btn.loading {
  pointer-events: none;
  position: relative;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Focus states for accessibility */
.users-table th.sortable:focus {
  outline: 3px solid rgba(42, 165, 157, 0.4);
  outline-offset: 2px;
}

.users-table th.sortable:focus:not(:focus-visible) {
  outline: none;
}

/* Animation for table rows */
.users-table tbody tr {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}