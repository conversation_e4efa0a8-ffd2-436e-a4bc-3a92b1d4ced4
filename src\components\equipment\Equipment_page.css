/* Equipment Page Styles */

/* Equipment Tab Container */
.equipment-tab {
  padding: 20px;
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.equipment-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #111827;
}

.category-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 8px;
}

.category-selector label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.category-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
  background-color: white;
  color: #374151;
}

.category-select:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Add Equipment Button */
.add-equipment-btn {
  padding: 10px 20px;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  cursor: pointer;
}

.add-equipment-btn.enabled {
  background-color: #10b981;
}

.add-equipment-btn.enabled:hover {
  background-color: #059669;
}

.add-equipment-btn.disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* Empty States */
.empty-state {
  padding: 40px;
  text-align: center;
  color: #6b7280;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 18px;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Equipment Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.modal-close-btn:hover {
  background-color: #f3f4f6;
}

/* Form Styles */
.form-group {
  margin-bottom: 15px;
}

.form-group:last-of-type {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-cancel {
  padding: 10px 20px;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s, border-color 0.2s;
}

.btn-cancel:hover {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

.btn-submit {
  padding: 10px 20px;
  background-color: #10b981;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-submit:hover {
  background-color: #059669;
}

/* Equipment Table */
.equipment-table-container {
  background-color: #fff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #fff;
}

.table-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.table-wrapper {
  overflow-x: auto;
}

.equipment-table {
  width: 100%;
  border-collapse: collapse;
}

.equipment-table thead tr {
  background-color: #f9fafb;
}

.equipment-table th {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  background-color: #f9fafb;
}

.equipment-table th.center {
  text-align: center;
}

.equipment-table tbody tr {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.equipment-table tbody tr:hover {
  background-color: #f9fafb;
}

.equipment-table td {
  padding: 16px;
  font-size: 14px;
  vertical-align: top;
  color: #374151;
}

.equipment-table td.center {
  text-align: center;
}

/* Table Actions */
.table-actions {
  display: flex;
  gap: 6px;
  justify-content: center;
}

.btn-edit {
  padding: 6px 12px;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s, border-color 0.2s;
}

.btn-edit:hover {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

.btn-delete {
  padding: 6px 12px;
  background-color: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s, border-color 0.2s;
}

.btn-delete:hover {
  background-color: #fecaca;
  border-color: #f87171;
}

/* Responsive Design */
@media (max-width: 768px) {
  .equipment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .category-selector {
    margin-top: 0;
  }
  
  .category-select {
    min-width: 100%;
  }
  
  .modal-content {
    padding: 20px;
    margin: 20px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-cancel,
  .btn-submit {
    width: 100%;
    justify-content: center;
  }
  
  .table-actions {
    flex-direction: column;
    gap: 4px;
  }
  
  .btn-edit,
  .btn-delete {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .equipment-tab {
    padding: 16px;
  }
  
  .equipment-table th,
  .equipment-table td {
    padding: 12px 8px;
    font-size: 12px;
  }
  
  .empty-state {
    padding: 30px 20px;
  }
  
  .empty-state-icon {
    font-size: 36px;
  }
}