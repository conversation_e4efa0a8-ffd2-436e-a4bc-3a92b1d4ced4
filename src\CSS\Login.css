/* src/CSS/Login.css */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: white;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-form {
  background: #2ca49f;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(44, 164, 159, 0.2);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.login-form h2 {
  color: white;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 700;
}

.login-subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 32px;
  font-size: 14px;
}

.input-group {
  margin-bottom: 20px;
  text-align: left;
}

.input-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: white;
  color: #1f2937;
}

.input-group input:focus {
  outline: none;
  border-color: white;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

.input-group input:disabled {
  background-color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.input-group input::placeholder {
  color: #9ca3af;
}

.login-button {
  width: 100%;
  padding: 12px 24px;
  background: white;
  color: #2ca49f;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(255, 255, 255, 0.3);
  background: #f8f9fa;
}

.login-button:disabled {
  background: rgba(255, 255, 255, 0.5);
  color: rgba(44, 164, 159, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.login-button.loading {
  position: relative;
}

.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin: 10px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.message.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ffffff;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.message.success {
  background-color: rgba(34, 197, 94, 0.1);
  color: #ffffff;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.error-icon,
.success-icon {
  font-size: 16px;
}

.login-footer {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.login-footer p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin: 0;
}

/* Responsive design */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-form {
    padding: 30px 20px;
  }
  
  .login-form h2 {
    font-size: 24px;
  }
}