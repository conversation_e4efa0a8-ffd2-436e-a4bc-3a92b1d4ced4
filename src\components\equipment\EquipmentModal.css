/* Equipment Modal Styles */

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

/* Modal Content Container */
.modal-content {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
  border: 1px solid #e5e7eb;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 30px 20px 30px;
  border-bottom: 2px solid #f1f5f9;
  position: relative;
}

.modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30px;
  right: 30px;
  height: 2px;
  background: #2aa59d;
}

.modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.modal-close-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  line-height: 1;
}

.modal-close-btn:hover {
  background: #ef4444;
  color: white;
  transform: scale(1.1);
}

/* Modal Form */
.modal-content form {
  padding: 30px;
}

/* Form Groups */
.form-group {
  margin-bottom: 24px;
}

.form-group:last-of-type {
  margin-bottom: 30px;
}

/* Form Labels */
.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-label::after {
  content: ' *';
  color: #ef4444;
  font-weight: 700;
}

.form-label:not([for="name"])::after {
  content: '';
}

/* Form Inputs */
.form-input,
.form-textarea {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #4b5563;
  border-radius: 12px;
  font-size: 16px;
  color: #1f2937;
  background: white;
  transition: all 0.3s ease;
  box-sizing: border-box;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #2aa59d;
  box-shadow: 0 0 0 4px rgba(42, 165, 157, 0.1);
  transform: translateY(-1px);
}

.form-input:hover,
.form-textarea:hover {
  border-color: #374151;
}

/* Textarea Specific */
.form-textarea {
  resize: vertical;
  min-height: 80px;
  line-height: 1.5;
}

/* Input States */
.form-input:invalid {
  border-color: #ef4444;
}

.form-input:invalid:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.form-input:valid {
  border-color: #10b981;
}

.form-input:valid:focus {
  border-color: #2aa59d;
  box-shadow: 0 0 0 4px rgba(42, 165, 157, 0.1);
}

/* Number Input Specific */
input[type="number"].form-input {
  -moz-appearance: textfield;
}

input[type="number"].form-input::-webkit-outer-spin-button,
input[type="number"].form-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* Action Buttons */
.btn-cancel,
.btn-submit {
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  position: relative;
  overflow: hidden;
}

/* Cancel Button */
.btn-cancel {
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #d1d5db;
}

.btn-cancel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(107, 114, 128, 0.1);
  transition: left 0.5s ease;
}

.btn-cancel:hover::before {
  left: 100%;
}

.btn-cancel:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  color: #1f2937;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.2);
}

/* Submit Button */
.btn-submit {
  background: #2aa59d;
  color: white;
  border: 2px solid #2aa59d;
}

.btn-submit::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: left 0.5s ease;
}

.btn-submit:hover::before {
  left: 100%;
}

.btn-submit:hover {
  background: #43b091;
  border-color: #43b091;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(42, 165, 157, 0.3);
}

.btn-submit:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(42, 165, 157, 0.3);
}

/* Button Disabled States */
.btn-submit:disabled {
  background: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-submit:disabled:hover {
  background: #9ca3af;
  border-color: #9ca3af;
  transform: none;
  box-shadow: none;
}

/* Loading State */
.btn-submit.loading {
  pointer-events: none;
  position: relative;
}

.btn-submit.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Form Validation Messages */
.form-error {
  color: #ef4444;
  font-size: 14px;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.form-error::before {
  content: '⚠️';
  font-size: 12px;
}

.form-success {
  color: #10b981;
  font-size: 14px;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.form-success::before {
  content: '✅';
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    margin: 10px;
    max-width: calc(100vw - 20px);
    border-radius: 16px;
  }
  
  .modal-header {
    padding: 25px 20px 15px 20px;
  }
  
  .modal-header::before {
    left: 20px;
    right: 20px;
  }
  
  .modal-header h2 {
    font-size: 22px;
  }
  
  .modal-content form {
    padding: 25px 20px;
  }
  
  .form-actions {
    flex-direction: column-reverse;
    gap: 10px;
  }
  
  .btn-cancel,
  .btn-submit {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-content {
    border-radius: 12px;
  }
  
  .modal-header {
    padding: 20px 15px 12px 15px;
  }
  
  .modal-header::before {
    left: 15px;
    right: 15px;
  }
  
  .modal-header h2 {
    font-size: 20px;
  }
  
  .modal-close-btn {
    width: 36px;
    height: 36px;
    font-size: 20px;
  }
  
  .modal-content form {
    padding: 20px 15px;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-input,
  .form-textarea {
    padding: 12px 14px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Focus Management */
.modal-content:focus {
  outline: none;
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Enhanced accessibility */
@media (prefers-reduced-motion: reduce) {
  .modal-content {
    animation: none;
  }
  
  .btn-cancel::before,
  .btn-submit::before {
    transition: none;
  }
  
  .btn-submit.loading::after {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .form-input,
  .form-textarea {
    border-color: #000000;
  }
  
  .form-input:focus,
  .form-textarea:focus {
    border-color: #000000;
    box-shadow: 0 0 0 2px #000000;
  }
}